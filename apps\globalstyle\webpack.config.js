const { merge } = require("webpack-merge");
const singleSpaDefaults = require("webpack-config-single-spa-react-ts");
const webpack = require("webpack");

module.exports = (webpackConfigEnv, argv) => {
  const defaultConfig = singleSpaDefaults({
    orgName: "ucrecruits",
    projectName: "globalstyle",
    webpackConfigEnv,
    argv,
  });

  return merge(defaultConfig, {
    devServer: {
      port: 3333,
    },
    module: {
      rules: [
        {
          test: /\.s[ac]ss$/i,
          use: [
            // Creates `style` nodes from JS strings
            "style-loader",
            // Translates CSS into CommonJS
            "css-loader",
            // Compiles Sass to CSS
            "sass-loader",
          ],
        },
        {
          test: /\.svg$/,
          use: ["file-loader"],
        },
      ],
    },
    plugins: [
      new webpack.DefinePlugin({
        'process.env.API_RECRUITMENT': JSON.stringify(process.env.API_RECRUITMENT),
        'process.env.API_ASSESSMENT': JSON.stringify(process.env.API_ASSESSMENT),
        'process.env.API_HR_ANALYTICS': JSON.stringify(process.env.API_HR_ANALYTICS),
        'process.env.HELLOSIGN_CLIENTID': JSON.stringify(process.env.HELLOSIGN_CLIENTID),
        'process.env.AUTH0_CLIENT_ID': JSON.stringify(process.env.AUTH0_CLIENT_ID),
        'process.env.AUTH0_DOMAIN': JSON.stringify(process.env.AUTH0_DOMAIN),
        'process.env.AUTH0_AUDIENCE': JSON.stringify(process.env.AUTH0_AUDIENCE),
        'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
      })
    ]
  });
};
