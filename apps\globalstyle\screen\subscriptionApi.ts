import axios from 'axios';
import { getEnv } from "@urecruits/api";
import { getConfigForAuthorization } from '../utils/getConfigForAuthorization';

const { API_RECRUITMENT } = getEnv();

const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const getCompanySubscriptionData = async (
  setSubscriptionData: (data: any) => void,
  setPackageType: (type: string | null) => void,
  setIsLoadingSubscription: (loading: boolean) => void,
  retry: number = 0
): Promise<void> => {
  const token = localStorage.getItem('token');

  try {
    const res = await axios(`${API_RECRUITMENT}/api/subscription/get-company-main-subscription`, getConfigForAuthorization());
    if (res?.data && Object.keys(res?.data).length > 0) {
      setSubscriptionData(res.data);
      setPackageType(res.data.packageType ?? null);
      setIsLoadingSubscription(false);
    }
  } catch (err: any) {
    if (err.response?.status === 401 && retry < 3) {
      await delay(1000);
      await getCompanySubscriptionData(setSubscriptionData, setPackageType, setIsLoadingSubscription, retry + 1);
    } else {
      setIsLoadingSubscription(false);
    }
  }
};
